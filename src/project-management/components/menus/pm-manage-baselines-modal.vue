<script setup>
const emit = defineEmits(['close']);

const state = reactive({
  freeze_table: -1,
});

const table_columns = [
  {
    accessorKey: 'name',
    header: 'Name',
    id: 'name',
  },
  {
    accessorKey: 'description',
    header: 'Description',
    id: 'description',
  },
  {
    accessorKey: 'created_on',
    header: 'Created on',
    id: 'created_on',
  },
  {
    accessorKey: 'created_by',
    header: 'Created by',
    id: 'created_by',
  },
  {
    accessorKey: 'context_menu',
    header: '',
    id: 'context_menu',
    size: '5',
    show_on_hover: 'true',
  },
];

const table_data = [
  {
    name: 'Baseline 1',
    description: 'Baseline description',
    created_on: '2021-01-01',
    created_by: 'Created by',
  },
  {
    name: 'Baseline 2',
    description: 'Baseline description',
    created_on: '2021-01-01',
    created_by: 'Created by',
  },
];
</script>

<template>
  <HawkModalContainer
    content_class="rounded-none w-full h-full"
    :options="{ escToClose: false }"
  >
    <Vueform
      size="sm"
      :display-errors="false"
      :display-messages="false"
      :columns="{
        default: {
          container: 12,
          label: 3,
          wrapper: 9,
        },
        sm: {
          label: 4,
        },
        md: {
          label: 4,
        },
        lg: {
          label: 4,
        },
      }"
    >
      <div class="col-span-12">
        <HawkModalHeader @close="emit('close')">
          <template #title>
            <div class="flex flex-col justify-start">
              {{ $t('Manage baselines') }}
            </div>
          </template>
          <template #right>
            <HawkButton class="ml-7" @click="openBlockFormPopup();">
              <IconHawkPlus />
              {{ $t("New baseline") }}
            </HawkButton>
          </template>
        </HawkModalHeader>
        <HawkModalContent class="!h-[calc(100vh-160px)] !max-h-[calc(100vh-160px)] pm-set-planned-values-content">
          <HawkTable
            :data="table_data"
            :columns="table_columns"
            :show_menu_header="false"
            :disable_resize="true"
            :freeze_table="state.freeze_table"
            is_gapless
          >
            <template #context_menu="data">
              <HawkMenu
                :items="[
                  {
                    label: $t('Rename'),
                    value: 'rename',
                    on_click: () => {
                      console.log('Rename');
                    },
                  },
                  {
                    label: $t('Delete'),
                    value: 'delete',
                    on_click: () => {
                      console.log('Delete')
                    },
                  },
                ]"
                position="fixed"
                additional_trigger_classes="!ring-0 !flex !items-center"
                @click.stop=""
                @open="state.freeze_table = data.data?.row?.id"
                @close="state.freeze_table = '-1'"
              >
                <template #trigger>
                  <IconHawkDotsVertical class="flex items-center text-gray-600" />
                </template>
              </HawkMenu>
            </template>
          </HawkTable>
        </HawkModalContent>
      </div>
    </Vueform>
  </HawkModalContainer>
</template>
