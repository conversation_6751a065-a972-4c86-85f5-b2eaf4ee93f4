<script setup>
const emit = defineEmits(['close', 'confirm']);

const form$ = ref(null);
</script>

<template>
  <HawkModalContainer>
    <Vueform
      ref="form$"
      size="sm"
      :show-errors="false"
      :show-messages="false"
      :columns="{
        default: { container: 12, label: 4, wrapper: 12 },
        sm: { container: 12, label: 4, wrapper: 12 },
        md: { container: 12, label: 4, wrapper: 12 },
      }"
    >
      <div class="col-span-12">
        <HawkModalHeader @close="emit('close')">
          <template #title>
            <div class="flex flex-col justify-start">
              {{ $t("Activate version") }}
            </div>
          </template>
        </HawkModalHeader>
        <HawkModalContent>
          <div class="flex flex-col">
            <ToggleElement
              name="override_actuals"
              :label="$t('Override actuals')"
              :description="$t('Enable this option to override all recorded actual progress data so far for the activities and reset to the selected version. This operation is irreversible.')"
              class="h-9 mb-16"
            />
            <ToggleElement
              name="create_baseline"
              :label="$t('Create baseline')"
              :description="$t('Save a snapshot of the current active schedule before activating the new version.')"
              class="h-9 mb-8"
            />
            <div class="ml-[182px]">
              <div>
                {{ $t('Baseline name') }}
              </div>
              <TextElement
              :label="$t('Baseline name')"
                :conditions="[['create_baseline', true]]"
                name="baseline_name"
                :rules="['required']"
                class="h-9"
              />
            </div>
            <!-- <TextElement
              :conditions="[['create_baseline', true]]"
              name="baseline_name"
              :rules="['required']"
              class="h-9"
            /> -->
          </div>
        </HawkModalContent>
        <HawkModalFooter>
          <template #right>
            <div class="flex justify-end w-full col-span-full">
              <ButtonElement
                name="cancel"
                class="mr-4"
                :secondary="true"
                @click="emit('close')"
              >
                {{ $t('Cancel') }}
              </ButtonElement>
              <ButtonElement
                name="confirm"
                @click="emit('confirm', form$.data.override_actuals)"
              >
                {{ $t('Confirm') }}
              </ButtonElement>
            </div>
          </template>
        </HawkModalFooter>
      </div>
    </Vueform>
  </HawkModalContainer>
</template>
